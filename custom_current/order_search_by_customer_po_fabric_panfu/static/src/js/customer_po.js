/** @odoo-module **/

import { publicWidget } from "@web/legacy/js/public/public_widget";
// import { jsonrpc } from "@web/core/network/rpc_service"; // Commented out as not currently used

/**
 * Customer PO Widget for Order Search functionality
 * Note: Cart-level customer PO functionality is handled by website_artextile_jsi
 * This widget is reserved for future order-level customer PO features if needed
 */
publicWidget.registry.CustomerPOWidget = publicWidget.Widget.extend({
    selector: '.order-search-customer-po', // Changed selector to avoid conflicts
    events: {
        'change .customer_po': '_onChangeCustomerPO',
    },

    /**
     * Handle customer PO input change for order-level customer PO
     * Currently disabled to avoid conflicts with website_artextile_jsi
     * @param {Event} _ev - The change event (unused)
     */
    _onChangeCustomerPO: function (_ev) {
        // This functionality is currently handled by website_artextile_jsi
        // at the order line level, which is more flexible
        console.log('Customer PO functionality is handled by website_artextile_jsi module');

        // Uncomment below if order-level customer PO is needed in the future
        /*
        import { jsonrpc } from "@web/core/network/rpc_service";
        const $input = $(_ev.currentTarget);
        const customerPO = $input.val();
        const orderID = $('#cart_products').data('order-id');

        // Update the customer PO on the sale order (order-level)
        jsonrpc('/web/dataset/call_kw', {
            model: 'sale.order',
            method: 'update_customer_po',
            args: [orderID, customerPO],
            kwargs: {},
        }).then(() => {
            console.log('Customer PO updated successfully');
        }).catch((error) => {
            console.error('Error updating Customer PO:', error);
        });
        */
    },
});